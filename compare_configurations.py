import pandas as pd
import torch
import numpy as np
from improved_ensemble_main import train_improved_ensemble
import json
import os
from datetime import datetime

def run_configuration_comparison():
    """
    Run experiments with different configurations to find the best setup
    """
    
    # Base configuration
    base_config = {
        'train_path': 'ABSA_Dataset/Res_ABSA/Train.csv',
        'val_path': 'ABSA_Dataset/Res_ABSA/Dev.csv',
        'test_path': 'ABSA_Dataset/Res_ABSA/Test.csv',
        'max_length': 256,
        'batch_size': 16,
        'num_epochs': 5,  # Reduced for comparison
        'learning_rate': 2e-5,
        'weight_decay': 0.01,
        'dropout': 0.3,
        'ensemble_weights': [0.6, 0.4],
        'use_attention_weights': False,
        'focal_gamma': 2.0,
        'label_smoothing': 0.1,
        'gradient_clipping': 1.0,
        'patience': 3,
        'use_augmentation': True,
        'use_class_weights': True
    }
    
    # Different configurations to test
    configurations = [
        {
            'name': 'baseline_ce',
            'config': {**base_config, 'loss_type': 'ce', 'use_class_weights': False, 'experiment_name': 'baseline_ce'}
        },
        {
            'name': 'weighted_ce',
            'config': {**base_config, 'loss_type': 'weighted_ce', 'experiment_name': 'weighted_ce'}
        },
        {
            'name': 'focal_loss',
            'config': {**base_config, 'loss_type': 'focal', 'focal_gamma': 2.0, 'experiment_name': 'focal_loss'}
        },
        {
            'name': 'focal_loss_gamma3',
            'config': {**base_config, 'loss_type': 'focal', 'focal_gamma': 3.0, 'experiment_name': 'focal_loss_gamma3'}
        },
        {
            'name': 'label_smoothing',
            'config': {**base_config, 'loss_type': 'label_smoothing', 'label_smoothing': 0.1, 'experiment_name': 'label_smoothing'}
        },
        {
            'name': 'combined_loss',
            'config': {**base_config, 'loss_type': 'combined', 'experiment_name': 'combined_loss'}
        },
        {
            'name': 'combined_loss_heavy_focal',
            'config': {**base_config, 'loss_type': 'combined', 'focal_gamma': 3.0, 'experiment_name': 'combined_loss_heavy_focal'}
        },
        {
            'name': 'attention_ensemble',
            'config': {**base_config, 'loss_type': 'combined', 'use_attention_weights': True, 'experiment_name': 'attention_ensemble'}
        }
    ]
    
    results = []
    
    print("Starting configuration comparison...")
    print(f"Testing {len(configurations)} different configurations")
    
    for i, config_info in enumerate(configurations):
        print(f"\n{'='*60}")
        print(f"Running configuration {i+1}/{len(configurations)}: {config_info['name']}")
        print(f"{'='*60}")
        
        try:
            # Run training
            ensemble_model, best_model_state, exp_dir = train_improved_ensemble(config_info['config'])
            
            # Store results
            result = {
                'name': config_info['name'],
                'config': config_info['config'],
                'exp_dir': exp_dir,
                'status': 'completed'
            }
            
            # Try to extract metrics from the experiment
            try:
                checkpoint_path = f"{exp_dir}/models/best_model.pth"
                if os.path.exists(checkpoint_path):
                    checkpoint = torch.load(checkpoint_path, map_location='cpu')
                    result['val_loss'] = checkpoint.get('val_loss', None)
                    result['val_f1'] = checkpoint.get('val_f1', None)
                    result['epoch'] = checkpoint.get('epoch', None)
            except Exception as e:
                print(f"Could not load metrics: {e}")
            
            results.append(result)
            
        except Exception as e:
            print(f"Error running configuration {config_info['name']}: {e}")
            results.append({
                'name': config_info['name'],
                'config': config_info['config'],
                'status': 'failed',
                'error': str(e)
            })
    
    # Save comparison results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"configuration_comparison_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Print summary
    print(f"\n{'='*60}")
    print("CONFIGURATION COMPARISON SUMMARY")
    print(f"{'='*60}")
    
    successful_results = [r for r in results if r['status'] == 'completed' and 'val_f1' in r]
    
    if successful_results:
        # Sort by validation F1 score
        successful_results.sort(key=lambda x: x.get('val_f1', 0), reverse=True)
        
        print(f"{'Rank':<4} {'Configuration':<25} {'Val F1':<8} {'Val Loss':<10} {'Epoch':<6}")
        print("-" * 60)
        
        for i, result in enumerate(successful_results):
            print(f"{i+1:<4} {result['name']:<25} {result.get('val_f1', 'N/A'):<8.4f} "
                  f"{result.get('val_loss', 'N/A'):<10.4f} {result.get('epoch', 'N/A'):<6}")
        
        print(f"\nBest configuration: {successful_results[0]['name']}")
        print(f"Best validation F1: {successful_results[0].get('val_f1', 'N/A'):.4f}")
        
    else:
        print("No successful experiments found.")
    
    failed_results = [r for r in results if r['status'] == 'failed']
    if failed_results:
        print(f"\nFailed experiments: {len(failed_results)}")
        for result in failed_results:
            print(f"  - {result['name']}: {result.get('error', 'Unknown error')}")
    
    print(f"\nDetailed results saved to: {results_file}")
    
    return results

def run_quick_test():
    """
    Run a quick test with just a few key configurations
    """
    base_config = {
        'train_path': 'ABSA_Dataset/Res_ABSA/Train.csv',
        'val_path': 'ABSA_Dataset/Res_ABSA/Dev.csv',
        'test_path': 'ABSA_Dataset/Res_ABSA/Test.csv',
        'max_length': 256,
        'batch_size': 16,
        'num_epochs': 3,  # Very short for quick test
        'learning_rate': 2e-5,
        'weight_decay': 0.01,
        'dropout': 0.3,
        'ensemble_weights': [0.6, 0.4],
        'use_attention_weights': False,
        'focal_gamma': 2.0,
        'label_smoothing': 0.1,
        'gradient_clipping': 1.0,
        'patience': 2,
        'use_augmentation': False,  # Disable for speed
        'use_class_weights': True
    }
    
    # Key configurations to test
    quick_configs = [
        {
            'name': 'baseline',
            'config': {**base_config, 'loss_type': 'ce', 'use_class_weights': False, 'experiment_name': 'quick_baseline'}
        },
        {
            'name': 'weighted_ce',
            'config': {**base_config, 'loss_type': 'weighted_ce', 'experiment_name': 'quick_weighted_ce'}
        },
        {
            'name': 'combined_loss',
            'config': {**base_config, 'loss_type': 'combined', 'experiment_name': 'quick_combined'}
        }
    ]
    
    print("Running quick test with 3 configurations...")
    
    results = []
    for config_info in quick_configs:
        print(f"\nTesting: {config_info['name']}")
        try:
            ensemble_model, best_model_state, exp_dir = train_improved_ensemble(config_info['config'])
            
            # Try to get metrics
            checkpoint_path = f"{exp_dir}/models/best_model.pth"
            val_f1 = None
            if os.path.exists(checkpoint_path):
                checkpoint = torch.load(checkpoint_path, map_location='cpu')
                val_f1 = checkpoint.get('val_f1', None)
            
            results.append({
                'name': config_info['name'],
                'val_f1': val_f1,
                'exp_dir': exp_dir
            })
            
            print(f"Completed {config_info['name']} - Val F1: {val_f1:.4f if val_f1 else 'N/A'}")
            
        except Exception as e:
            print(f"Failed {config_info['name']}: {e}")
            results.append({
                'name': config_info['name'],
                'val_f1': None,
                'error': str(e)
            })
    
    # Print quick summary
    print(f"\n{'='*40}")
    print("QUICK TEST SUMMARY")
    print(f"{'='*40}")
    
    for result in results:
        if result.get('val_f1'):
            print(f"{result['name']}: F1 = {result['val_f1']:.4f}")
        else:
            print(f"{result['name']}: Failed")
    
    return results

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Compare different model configurations')
    parser.add_argument('--quick', action='store_true', help='Run quick test with fewer configurations')
    args = parser.parse_args()
    
    if args.quick:
        run_quick_test()
    else:
        run_configuration_comparison()

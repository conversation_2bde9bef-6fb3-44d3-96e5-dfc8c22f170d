import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import AutoModel, AutoTokenizer
from torch.utils.data import DataLoader

class PhoBERTModel(torch.nn.Module):
    def __init__(self, pretrained_model='vinai/phobert-base', num_aspects=4, hidden_size=768, dropout=0.3):
        super(PhoBERTModel, self).__init__()
        self.encoder = AutoModel.from_pretrained(pretrained_model)
        self.classifier = torch.nn.ModuleList([
            torch.nn.Sequential(
                torch.nn.Linear(hidden_size, hidden_size),
                torch.nn.ReLU(),
                torch.nn.Dropout(dropout),
                torch.nn.Linear(hidden_size, 4)  # 4 classes: None, Positive, Negative, Neutral
            ) for _ in range(num_aspects)
        ])
        
    def forward(self, input_ids, attention_mask, token_type_ids):
        outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask, token_type_ids=token_type_ids)
        pooled_output = outputs.last_hidden_state[:, 0]  # [CLS] token
        
        sentiment_logits = []
        for aspect_classifier in self.classifier:
            sentiment_logits.append(aspect_classifier(pooled_output))
            
        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]

class InfoXLMModel(torch.nn.Module):
    def __init__(self, pretrained_model='microsoft/infoxlm-base', num_aspects=4, hidden_size=768, dropout=0.3):
        super(InfoXLMModel, self).__init__()
        self.encoder = AutoModel.from_pretrained(pretrained_model)
        
        # Add layer normalization and improved classifier architecture
        self.layer_norm = torch.nn.LayerNorm(hidden_size)
        self.classifier = torch.nn.ModuleList([
            torch.nn.Sequential(
                torch.nn.Linear(hidden_size, hidden_size),
                torch.nn.LayerNorm(hidden_size),
                torch.nn.ReLU(),
                torch.nn.Dropout(dropout),
                torch.nn.Linear(hidden_size, hidden_size // 2),
                torch.nn.ReLU(),
                torch.nn.Dropout(dropout),
                torch.nn.Linear(hidden_size // 2, 4)  # 4 classes: None, Positive, Negative, Neutral
            ) for _ in range(num_aspects)
        ])
        
    def forward(self, input_ids, attention_mask):
        outputs = self.encoder(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = self.layer_norm(outputs.last_hidden_state[:, 0])  # [CLS] token with layer norm
        
        sentiment_logits = []
        for aspect_classifier in self.classifier:
            sentiment_logits.append(aspect_classifier(pooled_output))
            
        return torch.stack(sentiment_logits, dim=1)  # [batch_size, num_aspects, 4]

class ImprovedVotingEnsemble(torch.nn.Module):
    def __init__(self, num_aspects=4, models=None, weights=None, use_attention_weights=False):
        super(ImprovedVotingEnsemble, self).__init__()
        self.models = torch.nn.ModuleList(models) if models else torch.nn.ModuleList()
        self.weights = weights if weights else [1.0] * len(self.models)
        self.num_aspects = num_aspects
        self.use_attention_weights = use_attention_weights
        
        # Learnable attention weights for ensemble combination
        if use_attention_weights:
            self.attention_weights = torch.nn.Parameter(torch.ones(len(self.models)))
            
    def forward(self, batch):
        all_logits = []
        
        # Get predictions from PhoBERT model
        if len(self.models) > 0 and isinstance(self.models[0], PhoBERTModel):
            phobert_logits = self.models[0](
                input_ids=batch['phobert_input_ids'],
                attention_mask=batch['phobert_attention_mask'],
                token_type_ids=batch['phobert_token_type_ids']
            )
            all_logits.append(phobert_logits)
        
        # Get predictions from InfoXLM model
        if len(self.models) > 1 and isinstance(self.models[1], InfoXLMModel):
            infoxlm_logits = self.models[1](
                input_ids=batch['infoxlm_input_ids'],
                attention_mask=batch['infoxlm_attention_mask']
            )
            all_logits.append(infoxlm_logits)
        
        # Combine predictions
        if self.use_attention_weights:
            # Use learnable attention weights
            attention_weights = F.softmax(self.attention_weights, dim=0)
            weighted_logits = []
            for i, logits in enumerate(all_logits):
                weighted_logits.append(logits * attention_weights[i])
            ensemble_logits = sum(weighted_logits)
        else:
            # Use fixed weights
            weighted_logits = []
            for i, logits in enumerate(all_logits):
                weighted_logits.append(logits * self.weights[i])
            ensemble_logits = sum(weighted_logits) / sum(self.weights)
        
        return ensemble_logits

# Advanced Loss Functions for Class Imbalance
class FocalLoss(nn.Module):
    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if self.alpha.type() != inputs.data.type():
                self.alpha = self.alpha.type_as(inputs.data)
            at = self.alpha.gather(0, targets.data.view(-1))
            focal_loss = at * focal_loss
            
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class WeightedCrossEntropyLoss(nn.Module):
    def __init__(self, class_weights=None, reduction='mean'):
        super(WeightedCrossEntropyLoss, self).__init__()
        self.class_weights = class_weights
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        return F.cross_entropy(inputs, targets, weight=self.class_weights, reduction=self.reduction)

class LabelSmoothingLoss(nn.Module):
    def __init__(self, num_classes, smoothing=0.1, reduction='mean'):
        super(LabelSmoothingLoss, self).__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        log_probs = F.log_softmax(inputs, dim=-1)
        targets_one_hot = F.one_hot(targets, num_classes=self.num_classes).float()
        
        # Apply label smoothing
        targets_smooth = targets_one_hot * (1 - self.smoothing) + self.smoothing / self.num_classes
        
        loss = -torch.sum(targets_smooth * log_probs, dim=-1)
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss

class CombinedLoss(nn.Module):
    def __init__(self, focal_weight=0.5, ce_weight=0.3, smooth_weight=0.2, 
                 class_weights=None, focal_alpha=None, focal_gamma=2.0, 
                 smoothing=0.1, num_classes=4):
        super(CombinedLoss, self).__init__()
        self.focal_weight = focal_weight
        self.ce_weight = ce_weight
        self.smooth_weight = smooth_weight
        
        self.focal_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.ce_loss = WeightedCrossEntropyLoss(class_weights=class_weights)
        self.smooth_loss = LabelSmoothingLoss(num_classes=num_classes, smoothing=smoothing)
        
    def forward(self, inputs, targets):
        focal = self.focal_loss(inputs, targets)
        ce = self.ce_loss(inputs, targets)
        smooth = self.smooth_loss(inputs, targets)
        
        total_loss = (self.focal_weight * focal + 
                     self.ce_weight * ce + 
                     self.smooth_weight * smooth)
        
        return total_loss

def create_loss_function(loss_type='combined', class_weights=None, **kwargs):
    """
    Factory function to create different loss functions
    """
    if loss_type == 'focal':
        alpha = kwargs.get('focal_alpha', None)
        gamma = kwargs.get('focal_gamma', 2.0)
        return FocalLoss(alpha=alpha, gamma=gamma)
    
    elif loss_type == 'weighted_ce':
        return WeightedCrossEntropyLoss(class_weights=class_weights)
    
    elif loss_type == 'label_smoothing':
        num_classes = kwargs.get('num_classes', 4)
        smoothing = kwargs.get('smoothing', 0.1)
        return LabelSmoothingLoss(num_classes=num_classes, smoothing=smoothing)
    
    elif loss_type == 'combined':
        focal_alpha = kwargs.get('focal_alpha', class_weights)
        return CombinedLoss(
            class_weights=class_weights,
            focal_alpha=focal_alpha,
            focal_gamma=kwargs.get('focal_gamma', 2.0),
            smoothing=kwargs.get('smoothing', 0.1),
            num_classes=kwargs.get('num_classes', 4)
        )
    
    else:
        return nn.CrossEntropyLoss()

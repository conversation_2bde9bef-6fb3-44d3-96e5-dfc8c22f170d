import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer

class ImprovedDualTokenizerDataset(Dataset):
    def __init__(self, df, phobert_tokenizer, infoxlm_tokenizer, max_length=256):
        self.df = df
        self.phobert_tokenizer = phobert_tokenizer
        self.infoxlm_tokenizer = infoxlm_tokenizer
        self.max_length = max_length
        
        # Get aspect columns (excluding Review column)
        self.aspect_columns = [col for col in df.columns if col != 'Review']
        self.num_aspects = len(self.aspect_columns)
        
        # Map sentiment values to one-hot vectors
        self.sentiment_map = {
            0: [1, 0, 0, 0],  # None
            1: [0, 1, 0, 0],  # Positive
            2: [0, 0, 1, 0],  # Negative
            3: [0, 0, 0, 1]   # Neutral
        }

    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]
        text = row['Review']
        
        # Tokenize with PhoBERT
        phobert_encoding = self.phobert_tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Tokenize with InfoXLM
        infoxlm_encoding = self.infoxlm_tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Create labels tensor [num_aspects, 4] for sentiment classes
        labels = torch.zeros((self.num_aspects, 4))
        for i, aspect in enumerate(self.aspect_columns):
            sentiment_value = row[aspect]
            labels[i] = torch.tensor(self.sentiment_map[sentiment_value])
        
        return {
            # PhoBERT inputs
            'phobert_input_ids': phobert_encoding['input_ids'].squeeze(0),
            'phobert_attention_mask': phobert_encoding['attention_mask'].squeeze(0),
            'phobert_token_type_ids': phobert_encoding['token_type_ids'].squeeze(0),
            
            # InfoXLM inputs
            'infoxlm_input_ids': infoxlm_encoding['input_ids'].squeeze(0),
            'infoxlm_attention_mask': infoxlm_encoding['attention_mask'].squeeze(0),
            
            # Labels and metadata
            'labels': labels,
            'review_text': text
        }

    @staticmethod
    def collate_fn(batch):
        return {
            # PhoBERT batch
            'phobert_input_ids': torch.stack([x['phobert_input_ids'] for x in batch]),
            'phobert_attention_mask': torch.stack([x['phobert_attention_mask'] for x in batch]),
            'phobert_token_type_ids': torch.stack([x['phobert_token_type_ids'] for x in batch]),
            
            # InfoXLM batch
            'infoxlm_input_ids': torch.stack([x['infoxlm_input_ids'] for x in batch]),
            'infoxlm_attention_mask': torch.stack([x['infoxlm_attention_mask'] for x in batch]),
            
            # Labels and metadata
            'labels': torch.stack([x['labels'] for x in batch]),
            'review_text': [x['review_text'] for x in batch]
        }

def create_improved_dual_dataloaders(train_df, val_df, test_df, batch_size=32, max_length=256):
    """
    Create dataloaders with PhoBERT and InfoXLM tokenizers
    """
    # Initialize tokenizers
    phobert_tokenizer = AutoTokenizer.from_pretrained('vinai/phobert-base')
    infoxlm_tokenizer = AutoTokenizer.from_pretrained('microsoft/infoxlm-base')
    
    # Create datasets
    train_dataset = ImprovedDualTokenizerDataset(train_df, phobert_tokenizer, infoxlm_tokenizer, max_length)
    val_dataset = ImprovedDualTokenizerDataset(val_df, phobert_tokenizer, infoxlm_tokenizer, max_length)
    test_dataset = ImprovedDualTokenizerDataset(test_df, phobert_tokenizer, infoxlm_tokenizer, max_length)
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        collate_fn=ImprovedDualTokenizerDataset.collate_fn
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size,
        shuffle=False,
        collate_fn=ImprovedDualTokenizerDataset.collate_fn
    )
    test_loader = DataLoader(
        test_dataset, 
        batch_size=batch_size,
        shuffle=False,
        collate_fn=ImprovedDualTokenizerDataset.collate_fn
    )
    
    return train_loader, val_loader, test_loader

class DataAugmentation:
    """
    Simple data augmentation techniques for text
    """
    def __init__(self, augment_prob=0.1):
        self.augment_prob = augment_prob
    
    def synonym_replacement(self, text):
        """
        Simple synonym replacement (placeholder for more sophisticated methods)
        """
        # This is a simplified version - in practice, you'd use libraries like nlpaug
        # or implement more sophisticated augmentation
        return text
    
    def random_insertion(self, text):
        """
        Random insertion of words (placeholder)
        """
        return text
    
    def random_deletion(self, text):
        """
        Random deletion of words (placeholder)
        """
        words = text.split()
        if len(words) > 1 and torch.rand(1).item() < self.augment_prob:
            # Remove a random word
            idx_to_remove = torch.randint(0, len(words), (1,)).item()
            words.pop(idx_to_remove)
            return ' '.join(words)
        return text
    
    def augment_text(self, text):
        """
        Apply random augmentation
        """
        if torch.rand(1).item() < self.augment_prob:
            # Choose random augmentation method
            methods = [self.synonym_replacement, self.random_insertion, self.random_deletion]
            method = torch.randint(0, len(methods), (1,)).item()
            return methods[method](text)
        return text

class ImprovedAugmentedDataset(ImprovedDualTokenizerDataset):
    """
    Dataset with data augmentation for minority classes
    """
    def __init__(self, df, phobert_tokenizer, infoxlm_tokenizer, max_length=256, 
                 augment_minority=True, minority_threshold=0.1):
        super().__init__(df, phobert_tokenizer, infoxlm_tokenizer, max_length)
        self.augment_minority = augment_minority
        self.minority_threshold = minority_threshold
        self.augmenter = DataAugmentation()
        
        if augment_minority:
            self._identify_minority_samples()
    
    def _identify_minority_samples(self):
        """
        Identify samples with minority class sentiments for augmentation
        """
        self.minority_indices = []
        
        for idx, row in self.df.iterrows():
            has_minority = False
            for aspect in self.aspect_columns:
                sentiment_value = row[aspect]
                # Consider positive, negative, neutral as minority (not None)
                if sentiment_value in [1, 2, 3]:
                    has_minority = True
                    break
            
            if has_minority:
                self.minority_indices.append(idx)
    
    def __getitem__(self, idx):
        row = self.df.iloc[idx]
        text = row['Review']
        
        # Apply augmentation to minority class samples
        if self.augment_minority and idx in self.minority_indices:
            text = self.augmenter.augment_text(text)
        
        # Continue with normal processing
        # Tokenize with PhoBERT
        phobert_encoding = self.phobert_tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Tokenize with InfoXLM
        infoxlm_encoding = self.infoxlm_tokenizer(
            text,
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_tensors='pt'
        )
        
        # Create labels tensor [num_aspects, 4] for sentiment classes
        labels = torch.zeros((self.num_aspects, 4))
        for i, aspect in enumerate(self.aspect_columns):
            sentiment_value = row[aspect]
            labels[i] = torch.tensor(self.sentiment_map[sentiment_value])
        
        return {
            # PhoBERT inputs
            'phobert_input_ids': phobert_encoding['input_ids'].squeeze(0),
            'phobert_attention_mask': phobert_encoding['attention_mask'].squeeze(0),
            'phobert_token_type_ids': phobert_encoding['token_type_ids'].squeeze(0),
            
            # InfoXLM inputs
            'infoxlm_input_ids': infoxlm_encoding['input_ids'].squeeze(0),
            'infoxlm_attention_mask': infoxlm_encoding['attention_mask'].squeeze(0),
            
            # Labels and metadata
            'labels': labels,
            'review_text': text
        }

def create_augmented_dataloaders(train_df, val_df, test_df, batch_size=32, max_length=256, 
                               augment_training=True):
    """
    Create dataloaders with optional data augmentation for training set
    """
    # Initialize tokenizers
    phobert_tokenizer = AutoTokenizer.from_pretrained('vinai/phobert-base')
    infoxlm_tokenizer = AutoTokenizer.from_pretrained('microsoft/infoxlm-base')
    
    # Create datasets
    if augment_training:
        train_dataset = ImprovedAugmentedDataset(
            train_df, phobert_tokenizer, infoxlm_tokenizer, max_length, augment_minority=True
        )
    else:
        train_dataset = ImprovedDualTokenizerDataset(
            train_df, phobert_tokenizer, infoxlm_tokenizer, max_length
        )
    
    val_dataset = ImprovedDualTokenizerDataset(val_df, phobert_tokenizer, infoxlm_tokenizer, max_length)
    test_dataset = ImprovedDualTokenizerDataset(test_df, phobert_tokenizer, infoxlm_tokenizer, max_length)
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        collate_fn=ImprovedDualTokenizerDataset.collate_fn
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size,
        shuffle=False,
        collate_fn=ImprovedDualTokenizerDataset.collate_fn
    )
    test_loader = DataLoader(
        test_dataset, 
        batch_size=batch_size,
        shuffle=False,
        collate_fn=ImprovedDualTokenizerDataset.collate_fn
    )
    
    return train_loader, val_loader, test_loader

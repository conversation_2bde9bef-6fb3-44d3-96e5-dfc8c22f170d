import pandas as pd
import torch
import numpy as np
from transformers import AutoTokenizer
from sklearn.metrics import f1_score, precision_score, recall_score
from tqdm import tqdm
import os

from improved_ensemble_model import (
    PhoBERTModel, InfoXLMModel, ImprovedVotingEnsemble, 
    create_loss_function, FocalLoss, WeightedCrossEntropyLoss, CombinedLoss
)
from improved_ensemble_processor import create_improved_dual_dataloaders

def simple_preprocess_text(text):
    """
    Simple text preprocessing without Vietnamese-specific tools
    """
    if pd.isna(text):
        return ""
    
    # Basic cleaning
    text = str(text).strip()
    text = text.replace('\n', ' ').replace('\r', ' ')
    text = ' '.join(text.split())  # Remove extra whitespace
    
    return text

def load_and_simple_preprocess_data(train_path, val_path, test_path):
    """
    Load and preprocess data with simple preprocessing
    """
    print("Loading data...")
    
    # Load data
    df_train = pd.read_csv(train_path, encoding='utf8')
    df_val = pd.read_csv(val_path, encoding='utf8')
    df_test = pd.read_csv(test_path, encoding='utf8')
    
    # Simple preprocessing
    df_train['Review'] = df_train['Review'].apply(simple_preprocess_text)
    df_val['Review'] = df_val['Review'].apply(simple_preprocess_text)
    df_test['Review'] = df_test['Review'].apply(simple_preprocess_text)
    
    print(f"Train samples: {len(df_train)}")
    print(f"Val samples: {len(df_val)}")
    print(f"Test samples: {len(df_test)}")
    
    return df_train, df_val, df_test

def test_model_creation():
    """
    Test if models can be created successfully
    """
    print("Testing model creation...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    try:
        # Test PhoBERT model
        print("Creating PhoBERT model...")
        phobert_model = PhoBERTModel(
            pretrained_model='vinai/phobert-base',
            num_aspects=4,
            hidden_size=768,
            dropout=0.3
        ).to(device)
        print("✓ PhoBERT model created successfully")
        
        # Test InfoXLM model
        print("Creating InfoXLM model...")
        infoxlm_model = InfoXLMModel(
            pretrained_model='microsoft/infoxlm-base',
            num_aspects=4,
            hidden_size=768,
            dropout=0.3
        ).to(device)
        print("✓ InfoXLM model created successfully")
        
        # Test ensemble
        print("Creating ensemble model...")
        ensemble_model = ImprovedVotingEnsemble(
            num_aspects=4,
            models=[phobert_model, infoxlm_model],
            weights=[0.6, 0.4],
            use_attention_weights=False
        ).to(device)
        print("✓ Ensemble model created successfully")
        
        return ensemble_model, device
        
    except Exception as e:
        print(f"✗ Error creating models: {e}")
        return None, device

def test_loss_functions():
    """
    Test different loss functions
    """
    print("\nTesting loss functions...")
    
    # Load class weights if available
    class_weights = None
    if os.path.exists('class_weights.pt'):
        class_weights = torch.load('class_weights.pt')
        print(f"Loaded class weights: {class_weights}")
    
    try:
        # Test different loss functions
        loss_functions = {
            'cross_entropy': create_loss_function('ce'),
            'weighted_ce': create_loss_function('weighted_ce', class_weights=class_weights),
            'focal': create_loss_function('focal', focal_gamma=2.0),
            'label_smoothing': create_loss_function('label_smoothing', smoothing=0.1, num_classes=4),
            'combined': create_loss_function('combined', class_weights=class_weights, focal_gamma=2.0, smoothing=0.1, num_classes=4)
        }
        
        # Test with dummy data
        batch_size, num_aspects, num_classes = 4, 12, 4
        dummy_outputs = torch.randn(batch_size * num_aspects, num_classes)
        dummy_labels = torch.randint(0, num_classes, (batch_size * num_aspects,))
        
        for name, criterion in loss_functions.items():
            try:
                loss = criterion(dummy_outputs, dummy_labels)
                print(f"✓ {name}: loss = {loss.item():.4f}")
            except Exception as e:
                print(f"✗ {name}: error = {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing loss functions: {e}")
        return False

def test_data_loading():
    """
    Test data loading and preprocessing
    """
    print("\nTesting data loading...")
    
    try:
        # Load data
        df_train, df_val, df_test = load_and_simple_preprocess_data(
            'ABSA_Dataset/Res_ABSA/Train.csv',
            'ABSA_Dataset/Res_ABSA/Dev.csv',
            'ABSA_Dataset/Res_ABSA/Test.csv'
        )
        
        # Create small subset for testing
        df_train_small = df_train.head(100)
        df_val_small = df_val.head(50)
        df_test_small = df_test.head(50)
        
        print("Creating dataloaders...")
        train_loader, val_loader, test_loader = create_improved_dual_dataloaders(
            df_train_small, df_val_small, df_test_small,
            batch_size=8,
            max_length=128
        )
        
        print(f"✓ Train batches: {len(train_loader)}")
        print(f"✓ Val batches: {len(val_loader)}")
        print(f"✓ Test batches: {len(test_loader)}")
        
        # Test one batch
        for batch in train_loader:
            print(f"✓ Batch shape - PhoBERT input: {batch['phobert_input_ids'].shape}")
            print(f"✓ Batch shape - InfoXLM input: {batch['infoxlm_input_ids'].shape}")
            print(f"✓ Batch shape - Labels: {batch['labels'].shape}")
            break
        
        return train_loader, val_loader, test_loader
        
    except Exception as e:
        print(f"✗ Error in data loading: {e}")
        return None, None, None

def test_forward_pass(ensemble_model, train_loader, device):
    """
    Test forward pass through the ensemble model
    """
    print("\nTesting forward pass...")
    
    try:
        ensemble_model.eval()
        
        for batch in train_loader:
            # Move batch to device
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            
            # Forward pass
            with torch.no_grad():
                outputs = ensemble_model(batch)
            
            print(f"✓ Forward pass successful")
            print(f"✓ Output shape: {outputs.shape}")
            print(f"✓ Expected shape: [batch_size, num_aspects, num_classes]")
            
            # Test loss calculation
            batch_size, num_aspects, num_classes = outputs.shape
            outputs_reshaped = outputs.view(-1, num_classes)
            labels_reshaped = batch['labels'].view(-1, num_classes)
            labels_indices = torch.argmax(labels_reshaped, dim=1)
            
            criterion = torch.nn.CrossEntropyLoss()
            loss = criterion(outputs_reshaped, labels_indices)
            
            print(f"✓ Loss calculation successful: {loss.item():.4f}")
            break
        
        return True
        
    except Exception as e:
        print(f"✗ Error in forward pass: {e}")
        return False

def run_mini_training_test(ensemble_model, train_loader, val_loader, device):
    """
    Run a very short training test to verify everything works
    """
    print("\nRunning mini training test...")
    
    try:
        # Setup training
        optimizer = torch.optim.AdamW(ensemble_model.parameters(), lr=1e-4)
        
        # Load class weights if available
        class_weights = None
        if os.path.exists('class_weights.pt'):
            class_weights = torch.load('class_weights.pt').to(device)
        
        criterion = create_loss_function('combined', class_weights=class_weights, num_classes=4)
        
        # Train for 2 steps
        ensemble_model.train()
        
        for step, batch in enumerate(train_loader):
            if step >= 2:  # Only 2 steps
                break
                
            # Move batch to device
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            labels = batch['labels']
            
            # Forward pass
            optimizer.zero_grad()
            outputs = ensemble_model(batch)
            
            # Calculate loss
            batch_size, num_aspects, num_classes = outputs.shape
            outputs_reshaped = outputs.view(-1, num_classes)
            labels_reshaped = labels.view(-1, num_classes)
            labels_indices = torch.argmax(labels_reshaped, dim=1)
            
            loss = criterion(outputs_reshaped, labels_indices)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            print(f"✓ Training step {step + 1}: loss = {loss.item():.4f}")
        
        # Test evaluation
        ensemble_model.eval()
        val_loss = 0
        val_steps = 0
        
        with torch.no_grad():
            for step, batch in enumerate(val_loader):
                if step >= 2:  # Only 2 steps
                    break
                    
                batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
                labels = batch['labels']
                
                outputs = ensemble_model(batch)
                
                batch_size, num_aspects, num_classes = outputs.shape
                outputs_reshaped = outputs.view(-1, num_classes)
                labels_reshaped = labels.view(-1, num_classes)
                labels_indices = torch.argmax(labels_reshaped, dim=1)
                
                loss = criterion(outputs_reshaped, labels_indices)
                val_loss += loss.item()
                val_steps += 1
                
                print(f"✓ Validation step {step + 1}: loss = {loss.item():.4f}")
        
        avg_val_loss = val_loss / val_steps if val_steps > 0 else 0
        print(f"✓ Average validation loss: {avg_val_loss:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in mini training: {e}")
        return False

def main():
    """
    Run all tests
    """
    print("="*60)
    print("TESTING IMPROVED ENSEMBLE MODEL")
    print("="*60)
    
    # Test 1: Model creation
    ensemble_model, device = test_model_creation()
    if ensemble_model is None:
        print("Model creation failed. Stopping tests.")
        return
    
    # Test 2: Loss functions
    loss_test_passed = test_loss_functions()
    if not loss_test_passed:
        print("Loss function tests failed.")
    
    # Test 3: Data loading
    train_loader, val_loader, test_loader = test_data_loading()
    if train_loader is None:
        print("Data loading failed. Stopping tests.")
        return
    
    # Test 4: Forward pass
    forward_test_passed = test_forward_pass(ensemble_model, train_loader, device)
    if not forward_test_passed:
        print("Forward pass test failed.")
        return
    
    # Test 5: Mini training
    training_test_passed = run_mini_training_test(ensemble_model, train_loader, val_loader, device)
    if not training_test_passed:
        print("Mini training test failed.")
        return
    
    print("\n" + "="*60)
    print("ALL TESTS PASSED! 🎉")
    print("The improved ensemble model is ready for full training.")
    print("="*60)

if __name__ == "__main__":
    main()

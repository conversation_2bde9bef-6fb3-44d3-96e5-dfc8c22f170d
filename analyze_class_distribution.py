import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import torch

def analyze_class_distribution(csv_path, dataset_name="Dataset"):
    """
    Analyze class distribution in the ABSA dataset
    """
    print(f"\n=== {dataset_name} Class Distribution Analysis ===")
    
    # Load data
    df = pd.read_csv(csv_path, encoding='utf8')
    
    # Get aspect columns (excluding Review column)
    aspect_columns = [col for col in df.columns if col != 'Review']
    
    print(f"Number of samples: {len(df)}")
    print(f"Number of aspects: {len(aspect_columns)}")
    print(f"Aspect columns: {aspect_columns}")
    
    # Class mapping
    class_names = {0: 'None', 1: 'Positive', 2: 'Negative', 3: 'Neutral'}
    
    # Overall distribution across all aspects
    all_labels = []
    for aspect in aspect_columns:
        all_labels.extend(df[aspect].tolist())
    
    overall_distribution = Counter(all_labels)
    total_labels = len(all_labels)
    
    print(f"\n--- Overall Distribution Across All Aspects ---")
    for class_idx, count in sorted(overall_distribution.items()):
        percentage = (count / total_labels) * 100
        print(f"{class_names[class_idx]}: {count} ({percentage:.2f}%)")
    
    # Per-aspect distribution
    print(f"\n--- Per-Aspect Distribution ---")
    aspect_distributions = {}
    
    for aspect in aspect_columns:
        aspect_dist = Counter(df[aspect])
        aspect_distributions[aspect] = aspect_dist
        
        print(f"\n{aspect}:")
        for class_idx in [0, 1, 2, 3]:
            count = aspect_dist.get(class_idx, 0)
            percentage = (count / len(df)) * 100
            print(f"  {class_names[class_idx]}: {count} ({percentage:.2f}%)")
    
    return overall_distribution, aspect_distributions, aspect_columns

def calculate_class_weights(distribution, method='inverse_frequency'):
    """
    Calculate class weights for handling imbalance
    """
    total_samples = sum(distribution.values())
    num_classes = len(distribution)
    
    if method == 'inverse_frequency':
        # Inverse frequency weighting
        weights = {}
        for class_idx, count in distribution.items():
            if count > 0:
                weights[class_idx] = total_samples / (num_classes * count)
            else:
                weights[class_idx] = 1.0
    
    elif method == 'balanced':
        # Sklearn-style balanced weighting
        weights = {}
        for class_idx, count in distribution.items():
            if count > 0:
                weights[class_idx] = total_samples / (num_classes * count)
            else:
                weights[class_idx] = 1.0
    
    elif method == 'sqrt_inverse':
        # Square root of inverse frequency (less aggressive)
        weights = {}
        for class_idx, count in distribution.items():
            if count > 0:
                weights[class_idx] = np.sqrt(total_samples / (num_classes * count))
            else:
                weights[class_idx] = 1.0
    
    # Normalize weights so that the minimum weight is 1.0
    min_weight = min(weights.values())
    weights = {k: v / min_weight for k, v in weights.items()}
    
    return weights

def plot_distribution(overall_dist, aspect_distributions, aspect_columns, save_path=None):
    """
    Create visualization of class distributions
    """
    class_names = {0: 'None', 1: 'Positive', 2: 'Negative', 3: 'Neutral'}
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('ABSA Dataset Class Distribution Analysis', fontsize=16)
    
    # Overall distribution pie chart
    ax1 = axes[0, 0]
    labels = [class_names[i] for i in sorted(overall_dist.keys())]
    sizes = [overall_dist[i] for i in sorted(overall_dist.keys())]
    colors = ['lightcoral', 'lightgreen', 'lightblue', 'lightyellow']
    
    ax1.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    ax1.set_title('Overall Distribution Across All Aspects')
    
    # Overall distribution bar chart
    ax2 = axes[0, 1]
    x_pos = range(len(labels))
    ax2.bar(x_pos, sizes, color=colors)
    ax2.set_xlabel('Sentiment Classes')
    ax2.set_ylabel('Count')
    ax2.set_title('Overall Distribution (Bar Chart)')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(labels)
    
    # Per-aspect distribution heatmap
    ax3 = axes[1, 0]
    
    # Create matrix for heatmap
    heatmap_data = []
    for aspect in aspect_columns:
        row = []
        for class_idx in [0, 1, 2, 3]:
            count = aspect_distributions[aspect].get(class_idx, 0)
            row.append(count)
        heatmap_data.append(row)
    
    heatmap_data = np.array(heatmap_data)
    
    sns.heatmap(heatmap_data, 
                xticklabels=labels,
                yticklabels=aspect_columns,
                annot=True, 
                fmt='d',
                cmap='YlOrRd',
                ax=ax3)
    ax3.set_title('Per-Aspect Class Distribution')
    ax3.set_xlabel('Sentiment Classes')
    ax3.set_ylabel('Aspects')
    
    # Class imbalance ratio
    ax4 = axes[1, 1]
    
    # Calculate imbalance ratios (None vs Others)
    none_count = overall_dist[0]
    other_counts = [overall_dist[i] for i in [1, 2, 3]]
    
    imbalance_ratios = [none_count / count if count > 0 else 0 for count in other_counts]
    other_labels = ['Positive', 'Negative', 'Neutral']
    
    ax4.bar(other_labels, imbalance_ratios, color=['green', 'red', 'blue'])
    ax4.set_ylabel('Imbalance Ratio (None/Class)')
    ax4.set_title('Class Imbalance Ratios')
    ax4.axhline(y=1, color='black', linestyle='--', alpha=0.5, label='Balanced')
    ax4.legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {save_path}")
    
    plt.show()

def main():
    # Paths to dataset files
    train_path = 'ABSA_Dataset/Res_ABSA/Train.csv'
    val_path = 'ABSA_Dataset/Res_ABSA/Dev.csv'
    test_path = 'ABSA_Dataset/Res_ABSA/Test.csv'
    
    # Analyze each dataset
    datasets = [
        (train_path, "Training"),
        (val_path, "Validation"),
        (test_path, "Test")
    ]
    
    all_distributions = {}
    
    for path, name in datasets:
        try:
            overall_dist, aspect_dist, aspect_cols = analyze_class_distribution(path, name)
            all_distributions[name] = {
                'overall': overall_dist,
                'aspects': aspect_dist,
                'columns': aspect_cols
            }
        except FileNotFoundError:
            print(f"File not found: {path}")
            continue
    
    # Calculate class weights for training set
    if 'Training' in all_distributions:
        print(f"\n=== Class Weight Calculations ===")
        train_dist = all_distributions['Training']['overall']
        
        methods = ['inverse_frequency', 'balanced', 'sqrt_inverse']
        for method in methods:
            weights = calculate_class_weights(train_dist, method)
            print(f"\n{method.replace('_', ' ').title()} Weights:")
            class_names = {0: 'None', 1: 'Positive', 2: 'Negative', 3: 'Neutral'}
            for class_idx in sorted(weights.keys()):
                print(f"  {class_names[class_idx]}: {weights[class_idx]:.4f}")
        
        # Create visualization
        plot_distribution(
            train_dist, 
            all_distributions['Training']['aspects'],
            all_distributions['Training']['columns'],
            save_path='class_distribution_analysis.png'
        )
        
        # Save weights to file for use in training
        weights_balanced = calculate_class_weights(train_dist, 'balanced')
        weights_tensor = torch.tensor([weights_balanced[i] for i in range(4)], dtype=torch.float32)
        torch.save(weights_tensor, 'class_weights.pt')
        print(f"\nClass weights saved to 'class_weights.pt'")
        print(f"Weights tensor: {weights_tensor}")

if __name__ == "__main__":
    main()

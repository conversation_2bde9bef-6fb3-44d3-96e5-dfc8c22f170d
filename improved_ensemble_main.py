import pandas as pd
import torch
import torch.nn as nn
from transformers import AutoTokenizer
from sklearn.metrics import f1_score, precision_score, recall_score, classification_report
from tqdm import tqdm
import numpy as np
import os
import argparse
from datetime import datetime

from improved_ensemble_model import (
    PhoBERTModel, InfoXLMModel, ImprovedVotingEnsemble, 
    create_loss_function, FocalLoss, WeightedCrossEntropyLoss, CombinedLoss
)
from improved_ensemble_processor import create_improved_dual_dataloaders, create_augmented_dataloaders
from process import VietnameseTextPreprocessor, preprocess_and_tokenize
from EvaluationSystemByFile import evaluation_system_by_file

def setup_experiment(config):
    """
    Setup experiment configuration and paths
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = f"{config['experiment_name']}_{timestamp}"
    
    # Create experiment directory
    exp_dir = f"experiments/{experiment_name}"
    os.makedirs(exp_dir, exist_ok=True)
    os.makedirs(f"{exp_dir}/models", exist_ok=True)
    os.makedirs(f"{exp_dir}/predictions", exist_ok=True)
    
    return exp_dir, experiment_name

def load_and_preprocess_data(config):
    """
    Load and preprocess the dataset
    """
    print("Loading and preprocessing data...")
    
    # Initialize preprocessor
    vn_preprocessor = VietnameseTextPreprocessor(
        extra_teencodes={
            'khách sạn': ['ks'], 'nhà hàng': ['nhahang'], 'nhân viên': ['nv'],
        }, 
        max_correction_length=config['max_length']
    )
    
    # Load data
    df_train = pd.read_csv(config['train_path'], encoding='utf8')
    df_val = pd.read_csv(config['val_path'], encoding='utf8')
    df_test = pd.read_csv(config['test_path'], encoding='utf8')
    
    # Preprocess text
    df_train = preprocess_and_tokenize(df_train, "Review", vn_preprocessor, None, 
                                     config['batch_size'], config['max_length'])
    df_val = preprocess_and_tokenize(df_val, "Review", vn_preprocessor, None, 
                                   config['batch_size'], config['max_length'])
    df_test = preprocess_and_tokenize(df_test, "Review", vn_preprocessor, None, 
                                    config['batch_size'], config['max_length'])
    
    return df_train, df_val, df_test

def create_models_and_ensemble(config, num_aspects, device):
    """
    Create individual models and ensemble
    """
    print("Creating models...")
    
    # Initialize PhoBERT model
    phobert_model = PhoBERTModel(
        pretrained_model='vinai/phobert-base',
        num_aspects=num_aspects,
        hidden_size=768,
        dropout=config['dropout']
    ).to(device)
    
    # Initialize InfoXLM model
    infoxlm_model = InfoXLMModel(
        pretrained_model='microsoft/infoxlm-base',
        num_aspects=num_aspects,
        hidden_size=768,
        dropout=config['dropout']
    ).to(device)
    
    # Create ensemble model
    ensemble_model = ImprovedVotingEnsemble(
        num_aspects=num_aspects,
        models=[phobert_model, infoxlm_model],
        weights=config['ensemble_weights'],
        use_attention_weights=config['use_attention_weights']
    ).to(device)
    
    return ensemble_model

def create_loss_and_optimizer(config, ensemble_model, device):
    """
    Create loss function and optimizer
    """
    # Load class weights if available
    class_weights = None
    if config['use_class_weights'] and os.path.exists('class_weights.pt'):
        class_weights = torch.load('class_weights.pt').to(device)
        print(f"Loaded class weights: {class_weights}")
    
    # Create loss function
    criterion = create_loss_function(
        loss_type=config['loss_type'],
        class_weights=class_weights,
        focal_gamma=config['focal_gamma'],
        smoothing=config['label_smoothing'],
        num_classes=4
    )
    
    # Create optimizer
    optimizer = torch.optim.AdamW(
        ensemble_model.parameters(), 
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # Create scheduler
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=2, verbose=True
    )
    
    return criterion, optimizer, scheduler

def train_epoch(model, train_loader, optimizer, criterion, device, config):
    """
    Train for one epoch
    """
    model.train()
    total_loss = 0
    correct_predictions = 0
    total_predictions = 0
    
    progress_bar = tqdm(train_loader, desc='Training')
    
    for batch in progress_bar:
        # Move batch to device
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
        labels = batch['labels']
        
        # Forward pass
        optimizer.zero_grad()
        outputs = model(batch)
        
        # Calculate loss
        batch_size, num_aspects, num_classes = outputs.shape
        outputs_reshaped = outputs.view(-1, num_classes)
        labels_reshaped = labels.view(-1, num_classes)
        
        # Convert one-hot to class indices for loss calculation
        labels_indices = torch.argmax(labels_reshaped, dim=1)
        loss = criterion(outputs_reshaped, labels_indices)
        
        # Backward pass
        loss.backward()
        
        # Gradient clipping
        if config['gradient_clipping'] > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clipping'])
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # Calculate accuracy
        predictions = torch.argmax(outputs_reshaped, dim=1)
        correct_predictions += (predictions == labels_indices).sum().item()
        total_predictions += predictions.shape[0]
        
        # Update progress bar
        progress_bar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'acc': f'{correct_predictions/total_predictions:.4f}'
        })
    
    avg_loss = total_loss / len(train_loader)
    accuracy = correct_predictions / total_predictions
    
    return avg_loss, accuracy

def evaluate_model(model, val_loader, criterion, device):
    """
    Evaluate model on validation set
    """
    model.eval()
    total_loss = 0
    correct_predictions = 0
    total_predictions = 0
    
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc='Evaluating'):
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}
            labels = batch['labels']
            
            outputs = model(batch)
            
            batch_size, num_aspects, num_classes = outputs.shape
            outputs_reshaped = outputs.view(-1, num_classes)
            labels_reshaped = labels.view(-1, num_classes)
            
            # Convert one-hot to class indices
            labels_indices = torch.argmax(labels_reshaped, dim=1)
            loss = criterion(outputs_reshaped, labels_indices)
            
            total_loss += loss.item()
            
            predictions = torch.argmax(outputs_reshaped, dim=1)
            correct_predictions += (predictions == labels_indices).sum().item()
            total_predictions += predictions.shape[0]
            
            # Store for detailed metrics
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels_indices.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = correct_predictions / total_predictions
    
    # Calculate detailed metrics
    f1_macro = f1_score(all_labels, all_predictions, average='macro')
    f1_weighted = f1_score(all_labels, all_predictions, average='weighted')
    
    return avg_loss, accuracy, f1_macro, f1_weighted

def train_improved_ensemble(config):
    """
    Main training function
    """
    # Setup experiment
    exp_dir, exp_name = setup_experiment(config)
    print(f"Starting experiment: {exp_name}")
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Load and preprocess data
    df_train, df_val, df_test = load_and_preprocess_data(config)
    
    # Create dataloaders
    if config['use_augmentation']:
        train_loader, val_loader, test_loader = create_augmented_dataloaders(
            df_train, df_val, df_test,
            batch_size=config['batch_size'],
            max_length=config['max_length'],
            augment_training=True
        )
    else:
        train_loader, val_loader, test_loader = create_improved_dual_dataloaders(
            df_train, df_val, df_test,
            batch_size=config['batch_size'],
            max_length=config['max_length']
        )
    
    # Get number of aspects
    num_aspects = len([col for col in df_train.columns if col != 'Review'])
    print(f"Number of aspects: {num_aspects}")
    
    # Create models and ensemble
    ensemble_model = create_models_and_ensemble(config, num_aspects, device)
    
    # Create loss function and optimizer
    criterion, optimizer, scheduler = create_loss_and_optimizer(config, ensemble_model, device)
    
    # Training loop
    best_val_loss = float('inf')
    best_f1 = 0.0
    best_model_state = None
    patience_counter = 0
    
    print(f"\nStarting training for {config['num_epochs']} epochs...")
    
    for epoch in range(config['num_epochs']):
        print(f'\nEpoch {epoch + 1}/{config["num_epochs"]}')
        print('-' * 50)
        
        # Training phase
        train_loss, train_acc = train_epoch(ensemble_model, train_loader, optimizer, criterion, device, config)
        print(f'Training Loss: {train_loss:.4f}, Accuracy: {train_acc:.4f}')
        
        # Validation phase
        val_loss, val_acc, val_f1_macro, val_f1_weighted = evaluate_model(ensemble_model, val_loader, criterion, device)
        print(f'Validation Loss: {val_loss:.4f}, Accuracy: {val_acc:.4f}')
        print(f'Validation F1 Macro: {val_f1_macro:.4f}, F1 Weighted: {val_f1_weighted:.4f}')
        
        # Learning rate scheduling
        scheduler.step(val_loss)
        
        # Save best model based on F1 score
        if val_f1_macro > best_f1:
            best_f1 = val_f1_macro
            best_val_loss = val_loss
            best_model_state = ensemble_model.state_dict()
            patience_counter = 0
            print('Best model saved!')
            
            # Save checkpoint
            torch.save({
                'epoch': epoch,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_loss': val_loss,
                'val_f1': val_f1_macro,
                'config': config
            }, f'{exp_dir}/models/best_model.pth')
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= config['patience']:
            print(f'Early stopping triggered after {patience_counter} epochs without improvement')
            break
    
    print(f'\nTraining completed!')
    print(f'Best validation F1: {best_f1:.4f}')
    print(f'Best validation loss: {best_val_loss:.4f}')
    
    return ensemble_model, best_model_state, exp_dir

def generate_predictions_and_evaluate(model, test_loader, device, exp_dir, aspect_columns):
    """
    Generate predictions and evaluate using the official evaluation system
    """
    model.eval()
    all_predictions = []
    all_texts = []

    # Mapping from index to sentiment label
    idx_to_sentiment = {0: 'None', 1: 'positive', 2: 'negative', 3: 'neutral'}

    print("Generating predictions...")
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Generating predictions"):
            batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

            # Get review texts
            texts = batch.get('review_text', [])
            all_texts.extend(texts)

            # Forward pass
            outputs = model(batch)

            # Get predictions
            predictions = torch.argmax(outputs, dim=-1).cpu().numpy()
            all_predictions.append(predictions)

    # Combine all predictions
    all_predictions = np.vstack(all_predictions)

    # Generate prediction file
    test_output_path = f"{exp_dir}/predictions/test_predictions.txt"
    gold_output_path = f"{exp_dir}/predictions/gold_labels.txt"

    # Write predictions to file
    with open(test_output_path, 'w', encoding='utf-8') as f:
        for i in range(len(all_texts)):
            f.write(f"#{i+1}\n")
            f.write(f"{all_texts[i]}\n")

            # Create aspect-sentiment pairs
            aspects = []
            for j, aspect in enumerate(aspect_columns):
                sentiment_idx = all_predictions[i][j]
                if sentiment_idx != 0:  # Skip 'None' sentiment
                    aspects.append(f"{{{aspect}, {idx_to_sentiment[sentiment_idx]}}}")

            # Write aspects line
            if aspects:
                f.write(", ".join(aspects) + "\n\n")
            else:
                f.write("\n\n")

    # Generate gold labels file (from test loader)
    all_gold_labels = []
    all_gold_texts = []

    for batch in test_loader:
        texts = batch.get('review_text', [])
        labels = batch['labels'].cpu().numpy()

        all_gold_texts.extend(texts)
        all_gold_labels.append(labels)

    all_gold_labels = np.vstack(all_gold_labels)

    with open(gold_output_path, 'w', encoding='utf-8') as f:
        for i in range(len(all_gold_texts)):
            f.write(f"#{i+1}\n")
            f.write(f"{all_gold_texts[i]}\n")

            # Create aspect-sentiment pairs from gold labels
            aspects = []
            for j, aspect in enumerate(aspect_columns):
                # Convert one-hot to class index
                sentiment_idx = np.argmax(all_gold_labels[i][j])
                if sentiment_idx != 0:  # Skip 'None' sentiment
                    aspects.append(f"{{{aspect}, {idx_to_sentiment[sentiment_idx]}}}")

            # Write aspects line
            if aspects:
                f.write(", ".join(aspects) + "\n\n")
            else:
                f.write("\n\n")

    # Evaluate using the official evaluation system
    print("\nEvaluating using official evaluation system:")
    print("-" * 50)
    evaluation_system_by_file(gold_output_path, test_output_path)

    return test_output_path, gold_output_path

def main():
    # Configuration
    config = {
        'experiment_name': 'improved_infoxlm_ensemble',
        'train_path': 'ABSA_Dataset/Res_ABSA/Train.csv',
        'val_path': 'ABSA_Dataset/Res_ABSA/Dev.csv',
        'test_path': 'ABSA_Dataset/Res_ABSA/Test.csv',
        'max_length': 256,
        'batch_size': 16,
        'num_epochs': 10,
        'learning_rate': 2e-5,
        'weight_decay': 0.01,
        'dropout': 0.3,
        'ensemble_weights': [0.6, 0.4],  # PhoBERT, InfoXLM
        'use_attention_weights': False,
        'loss_type': 'combined',  # 'focal', 'weighted_ce', 'label_smoothing', 'combined'
        'use_class_weights': True,
        'focal_gamma': 2.0,
        'label_smoothing': 0.1,
        'gradient_clipping': 1.0,
        'patience': 3,
        'use_augmentation': True
    }

    # Train the improved ensemble
    ensemble_model, best_model_state, exp_dir = train_improved_ensemble(config)

    # Load best model for evaluation
    ensemble_model.load_state_dict(best_model_state)

    # Load test data for evaluation
    df_test = pd.read_csv(config['test_path'], encoding='utf8')
    aspect_columns = [col for col in df_test.columns if col != 'Review']

    # Create test dataloader
    vn_preprocessor = VietnameseTextPreprocessor(
        extra_teencodes={'khách sạn': ['ks'], 'nhà hàng': ['nhahang'], 'nhân viên': ['nv']},
        max_correction_length=config['max_length']
    )
    df_test = preprocess_and_tokenize(df_test, "Review", vn_preprocessor, None,
                                    config['batch_size'], config['max_length'])

    _, _, test_loader = create_improved_dual_dataloaders(
        df_test, df_test, df_test,  # Use test set for all
        batch_size=config['batch_size'],
        max_length=config['max_length']
    )

    # Generate predictions and evaluate
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    generate_predictions_and_evaluate(ensemble_model, test_loader, device, exp_dir, aspect_columns)

    print(f"Experiment completed. Results saved in: {exp_dir}")

if __name__ == "__main__":
    main()
